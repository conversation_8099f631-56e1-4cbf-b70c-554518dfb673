<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('teacher_assignments', function (Blueprint $table) {
            // Add additional columns for better data tracking
            $table->string('assignment_note')->nullable()->after('is_homeroom_teacher');
            $table->enum('status', ['active', 'inactive', 'pending'])->default('active')->after('assignment_note');
            $table->date('start_date')->nullable()->after('status');
            $table->date('end_date')->nullable()->after('start_date');
            $table->integer('teaching_hours_per_week')->nullable()->after('end_date');
            $table->text('special_requirements')->nullable()->after('teaching_hours_per_week');

            // Add indexes for better performance
            $table->index(['teacher_id', 'academic_year_id']);
            $table->index(['classroom_id', 'academic_year_id']);
            $table->index(['subject_id', 'academic_year_id']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('teacher_assignments', function (Blueprint $table) {
            // Drop indexes if they exist
            try {
                $table->dropIndex(['teacher_id', 'academic_year_id']);
            } catch (Exception $e) {
                // Index doesn't exist, continue
            }

            try {
                $table->dropIndex(['classroom_id', 'academic_year_id']);
            } catch (Exception $e) {
                // Index doesn't exist, continue
            }

            try {
                $table->dropIndex(['subject_id', 'academic_year_id']);
            } catch (Exception $e) {
                // Index doesn't exist, continue
            }

            try {
                $table->dropIndex(['teacher_assignments_status_index']);
            } catch (Exception $e) {
                // Index doesn't exist, continue
            }

            // Drop columns if they exist
            if (Schema::hasColumn('teacher_assignments', 'assignment_note')) {
                $table->dropColumn('assignment_note');
            }
            if (Schema::hasColumn('teacher_assignments', 'status')) {
                $table->dropColumn('status');
            }
            if (Schema::hasColumn('teacher_assignments', 'start_date')) {
                $table->dropColumn('start_date');
            }
            if (Schema::hasColumn('teacher_assignments', 'end_date')) {
                $table->dropColumn('end_date');
            }
            if (Schema::hasColumn('teacher_assignments', 'teaching_hours_per_week')) {
                $table->dropColumn('teaching_hours_per_week');
            }
            if (Schema::hasColumn('teacher_assignments', 'special_requirements')) {
                $table->dropColumn('special_requirements');
            }
        });
    }
};
